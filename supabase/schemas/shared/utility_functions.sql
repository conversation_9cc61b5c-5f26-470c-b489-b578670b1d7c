-- Shared Utility Functions
-- This file contains utility functions used across multiple tables and schemas

-- Utility function for updating timestamps
CREATE OR REPLACE FUNCTION "public"."update_updated_at_column" () RETURNS "trigger" LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$ 
BEGIN 
	NEW.updated_at = timezone('utc', now());
	RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."update_updated_at_column" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."update_updated_at_column" () IS 'Updates the updated_at timestamp when a record is modified';

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION "public"."handle_new_user" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ 
BEGIN
	INSERT INTO public.profile (user_id, email, full_name)
	VALUES (
		NEW.id,
		NEW.email,
		NEW.raw_user_meta_data->>'full_name'
	);
	RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."handle_new_user" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."handle_new_user" () IS 'Automatically creates a profile when a new user signs up';
