-- Extensions and Custom Types
-- This file contains all PostgreSQL extensions and custom types used throughout the schema
-- PostgreSQL Extensions
CREATE EXTENSION IF NOT EXISTS "pg_net"
WITH
	SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pg_graphql"
WITH
	SCHEMA "graphql";

CREATE EXTENSION IF NOT EXISTS "pg_stat_statements"
WITH
	SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgcrypto"
WITH
	SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgjwt"
WITH
	SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "supabase_vault"
WITH
	SCHEMA "vault";

CREATE EXTENSION IF NOT EXISTS "uuid-ossp"
WITH
	SCHEMA "extensions";

-- Custom Types
CREATE TYPE "public"."checklist_item_status" AS ENUM('Incomplete', 'Deferred', 'Complete');

ALTER TYPE "public"."checklist_item_status" OWNER TO "postgres";

CREATE TYPE "public"."entity_type" AS ENUM('organization', 'client', 'project');

ALTER TYPE "public"."entity_type" OWNER TO "postgres";

CREATE TYPE "public"."invite_resource_type" AS ENUM('organization', 'client', 'project');

ALTER TYPE "public"."invite_resource_type" OWNER TO "postgres";

CREATE TYPE "public"."invite_status" AS ENUM(
	'pending',
	'accepted',
	'revoked',
	'expired',
	'declined'
);

ALTER TYPE "public"."invite_status" OWNER TO "postgres";

CREATE TYPE "public"."membership_role" AS ENUM('viewer', 'editor', 'admin', 'owner');

ALTER TYPE "public"."membership_role" OWNER TO "postgres";

CREATE TYPE "public"."wbs_item_type" AS ENUM('Standard', 'Custom');

ALTER TYPE "public"."wbs_item_type" OWNER TO "postgres";

-- Schema Comments
COMMENT ON SCHEMA "public" IS 'standard public schema';
