-- User Profiles and Authentication
-- This file contains user profile management and authentication-related functions
-- Profile table
CREATE TABLE IF NOT EXISTS "public"."profile" (
	"user_id" "uuid" NOT NULL,
	"email" "text" NOT NULL,
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."profile" OWNER TO "postgres";

COMMENT ON TABLE "public"."profile" IS 'User profile containing personal information and preferences';

-- Primary key constraint
ALTER TABLE ONLY "public"."profile"
ADD CONSTRAINT "profile_pkey" PRIMARY KEY ("user_id");

-- Enable Row Level Security
ALTER TABLE "public"."profile" ENABLE ROW LEVEL SECURITY;

-- Utility function for updating timestamps
CREATE OR REPLACE FUNCTION "public"."update_updated_at_column" () RETURNS "trigger" LANGUAGE "plpgsql"
SET
	"search_path" TO '' AS $$ 
BEGIN 
	NEW.updated_at = timezone('utc', now());
	RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."update_updated_at_column" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."update_updated_at_column" () IS 'Updates the updated_at timestamp when a record is modified';

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION "public"."handle_new_user" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ 
BEGIN
	INSERT INTO public.profile (user_id, email, full_name)
	VALUES (
		NEW.id,
		NEW.email,
		NEW.raw_user_meta_data->>'full_name'
	);
	RETURN NEW;
END;
$$;

ALTER FUNCTION "public"."handle_new_user" () OWNER TO "postgres";

-- Triggers
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."profile" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();
