-- Profile Table Schema
-- Contains user profile information and personal data

-- Profile table
CREATE TABLE IF NOT EXISTS "public"."profile" (
	"user_id" "uuid" NOT NULL,
	"email" "text" NOT NULL,
	"full_name" "text",
	"avatar_url" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."profile" OWNER TO "postgres";

COMMENT ON TABLE "public"."profile" IS 'User profile containing personal information and preferences';

-- Primary key constraint
ALTER TABLE ONLY "public"."profile"
ADD CONSTRAINT "profile_pkey" PRIMARY KEY ("user_id");

-- Enable Row Level Security
ALTER TABLE "public"."profile" ENABLE ROW LEVEL SECURITY;

-- Triggers
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."profile" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Users can insert their own profile" ON "public"."profile" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);

CREATE POLICY "Users can update their own profile" ON "public"."profile"
FOR UPDATE
	TO "authenticated" USING (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);

CREATE POLICY "Users can view their own profile" ON "public"."profile" FOR
SELECT
	TO "authenticated" USING (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);
