-- Budget Line Item Current Table Schema
-- Current state of project budget line items

-- Budget Line Item Current table
CREATE TABLE IF NOT EXISTS "public"."budget_line_item_current" (
	"budget_line_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"wbs_library_item_id" "uuid" NOT NULL,
	"quantity" numeric(20, 4) NOT NULL,
	"unit" "text",
	"material_rate" numeric(20, 4) NOT NULL,
	"labor_rate" numeric(20, 4),
	"productivity_per_hour" numeric(20, 4),
	"unit_rate_manual_override" boolean DEFAULT false NOT NULL,
	"unit_rate" numeric(20, 4) NOT NULL,
	"factor" numeric(20, 4),
	"remarks" "text",
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."budget_line_item_current" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_line_item_current" IS 'Current state of project budget line items';

-- Primary key constraint
ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_pkey" PRIMARY KEY ("budget_line_item_id");

-- Unique constraint on project_id and wbs_library_item_id
ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_project_id_wbs_library_item_id_key" UNIQUE ("project_id", "wbs_library_item_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "budget_line_item_current_project_id_idx" ON "public"."budget_line_item_current" USING "btree" ("project_id");

CREATE INDEX "budget_line_item_current_wbs_library_item_id_idx" ON "public"."budget_line_item_current" USING "btree" ("wbs_library_item_id");

-- Enable Row Level Security
ALTER TABLE "public"."budget_line_item_current" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_line_item_current" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Project editors can insert budget line item" ON "public"."budget_line_item_current" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can update budget line item" ON "public"."budget_line_item_current"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
		)
	)
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can delete budget line item" ON "public"."budget_line_item_current" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
	)
);

CREATE POLICY "Project viewers can view budget line item" ON "public"."budget_line_item_current" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" ("budget_line_item_current"."project_id") AS "can_access_project"
		)
	);
