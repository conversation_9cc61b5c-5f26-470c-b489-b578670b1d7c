-- WBS Library Table Schema
-- Work breakdown structure library containing standardized cost lines for projects

-- WBS Library table
CREATE TABLE IF NOT EXISTS "public"."wbs_library" (
	"wbs_library_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."wbs_library" OWNER TO "postgres";

COMMENT ON TABLE "public"."wbs_library" IS 'Work breakdown structure library containing standardized cost lines for projects.';

-- Primary key constraint
ALTER TABLE ONLY "public"."wbs_library"
ADD CONSTRAINT "wbs_library_pkey" PRIMARY KEY ("wbs_library_id");

-- Unique constraint on name
ALTER TABLE ONLY "public"."wbs_library"
ADD CONSTRAINT "wbs_library_name_key" UNIQUE ("name");

-- Enable Row Level Security
ALTER TABLE "public"."wbs_library" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."wbs_library" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Users can view WBS library" ON "public"."wbs_library" FOR
SELECT
	TO "authenticated" USING (true);

CREATE POLICY "Service role can insert WBS library" ON "public"."wbs_library" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Service role can update WBS library" ON "public"."wbs_library"
FOR UPDATE
	TO "service_role" USING (true);

CREATE POLICY "Service role can delete WBS library" ON "public"."wbs_library" FOR DELETE TO "service_role" USING (true);
