-- Project Gateway Stage Info Table Schema
-- Stores building details for a project's gateway stage

-- Project Gateway Stage Info table
CREATE TABLE IF NOT EXISTS "public"."project_gateway_stage_info" (
	"project_gateway_stage_info_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_stage_id" "uuid" NOT NULL,
	"basement_floors" numeric(20, 4),
	"ground_floor" numeric(20, 4),
	"upper_floors" numeric(20, 4),
	"total_gross_internal_floor_area" numeric(20, 4),
	"usable_area" numeric(20, 4),
	"circulation_area" numeric(20, 4),
	"ancillary_areas" numeric(20, 4),
	"internal_divisions" numeric(20, 4),
	"spaces_not_enclosed" numeric(20, 4),
	"total_gross_internal_floor_area_2" numeric(20, 4),
	"internal_cube" numeric(20, 4),
	"area_of_lowest_floor" numeric(20, 4),
	"site_area" numeric(20, 4),
	"number_of_units" numeric(20, 4),
	"nr_of_storeys" numeric(20, 4),
	"nr_of_storeys_primary" numeric(20, 4),
	"nr_of_storeys_secondary" numeric(20, 4),
	"basement_storeys_included_above" numeric(20, 4),
	"average_storey_height" numeric(20, 4),
	"below_ground_floors" numeric(20, 4),
	"ground_floor_height" numeric(20, 4),
	"above_ground_floors" numeric(20, 4),
	"external_vertical_envelope" numeric(20, 4),
	"additional_data" "jsonb",
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."project_gateway_stage_info" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_gateway_stage_info" IS 'Stores building details for a project''s gateway stage';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."basement_floors" IS 'Basement floors area in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."ground_floor" IS 'Ground floor area in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."upper_floors" IS 'Upper floors area in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."total_gross_internal_floor_area" IS 'Total (Gross Internal Floor Area) in m²';

COMMENT ON COLUMN "public"."project_gateway_stage_info"."usable_area" IS 'Usable Area in m²';

-- Primary key constraint
ALTER TABLE ONLY "public"."project_gateway_stage_info"
ADD CONSTRAINT "project_gateway_stage_info_pkey" PRIMARY KEY ("project_gateway_stage_info_id");

-- Unique constraint on project stage (one gateway info per stage)
ALTER TABLE ONLY "public"."project_gateway_stage_info"
ADD CONSTRAINT "project_gateway_stage_info_project_stage_id_key" UNIQUE ("project_stage_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."project_gateway_stage_info"
ADD CONSTRAINT "project_gateway_stage_info_project_stage_id_fkey" FOREIGN KEY ("project_stage_id") REFERENCES "public"."project_stage" ("project_stage_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project_gateway_stage_info"
ADD CONSTRAINT "project_gateway_stage_info_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "project_gateway_stage_info_stage_idx" ON "public"."project_gateway_stage_info" USING "btree" ("project_stage_id");

-- Enable Row Level Security
ALTER TABLE "public"."project_gateway_stage_info" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."project_gateway_stage_info" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Users can insert gateway stage info for projects they can edit" ON "public"."project_gateway_stage_info" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_modify_project" ("ps"."project_id")
					)
			)
		)
	);

CREATE POLICY "Users can update gateway stage info for projects they can edit" ON "public"."project_gateway_stage_info"
FOR UPDATE
	USING (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_modify_project" ("ps"."project_id")
					)
			)
		)
	)
WITH
	CHECK (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_modify_project" ("ps"."project_id")
					)
			)
		)
	);

CREATE POLICY "Users can delete gateway stage info for projects they can edit" ON "public"."project_gateway_stage_info" FOR DELETE USING (
	(
		EXISTS (
			SELECT
				1
			FROM
				"public"."project_stage" "ps"
			WHERE
				(
					(
						"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
					)
					AND "public"."current_user_has_entity_role" (
						'project'::"public"."entity_type",
						"ps"."project_id",
						'owner'::"public"."membership_role"
					)
				)
		)
	)
);

CREATE POLICY "Users can view stage info for projects they can view" ON "public"."project_gateway_stage_info" FOR
SELECT
	USING (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_access_project" ("ps"."project_id")
					)
			)
		)
	);
