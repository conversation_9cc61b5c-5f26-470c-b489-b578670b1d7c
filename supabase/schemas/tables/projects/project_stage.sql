-- Project Stage Table Schema
-- Contains project stage information for organizing work phases

-- Project Stage table
CREATE TABLE IF NOT EXISTS "public"."project_stage" (
	"project_stage_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"stage_order" integer NOT NULL,
	"stage" integer,
	"gateway_qualitative_scorecard" "jsonb",
	"date_started" timestamp with time zone,
	"date_completed" timestamp with time zone,
	"completion_notes" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."project_stage" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_stage" IS 'Project stages for organizing work phases';

-- Primary key constraint
ALTER TABLE ONLY "public"."project_stage"
ADD CONSTRAINT "project_stage_pkey" PRIMARY KEY ("project_stage_id");

-- Unique constraint on stage order within project
ALTER TABLE ONLY "public"."project_stage"
ADD CONSTRAINT "project_stage_project_id_stage_order_key" UNIQUE ("project_id", "stage_order");

-- Foreign key constraint
ALTER TABLE ONLY "public"."project_stage"
ADD CONSTRAINT "project_stage_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Enable Row Level Security
ALTER TABLE "public"."project_stage" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."project_stage" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Row Level Security Policies
CREATE POLICY "Project Editors and Owners can insert project stage" ON "public"."project_stage" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project Editors and Owners can update project stage" ON "public"."project_stage"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
		)
	)
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project Editors and Owners can delete project stage" ON "public"."project_stage" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
	)
);

CREATE POLICY "Project Viewers, Editors, and Owners can view project stage" ON "public"."project_stage" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" ("project_stage"."project_id") AS "can_access_project"
		)
	);
