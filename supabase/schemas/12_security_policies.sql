-- Row Level Security Policies
-- This file contains all RLS policies for hierarchical access control across the application
-- Profile Policies
CREATE POLICY "Users can insert their own profile" ON "public"."profile" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);

CREATE POLICY "Users can update their own profile" ON "public"."profile"
FOR UPDATE
	TO "authenticated" USING (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);

CREATE POLICY "Users can view their own profile" ON "public"."profile" FOR
SELECT
	TO "authenticated" USING (
		(
			"user_id" = (
				SELECT
					"auth"."uid" () AS "uid"
			)
		)
	);

-- Organization Policies
CREATE POLICY "Users can create organizations" ON "public"."organization" FOR INSERT TO "authenticated"
WITH
	CHECK (("created_by_user_id" = "auth"."uid" ()));

CREATE POLICY "Users can update their own organization" ON "public"."organization"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'organization'::"public"."entity_type",
			"org_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Users can view organizations they have access to" ON "public"."organization" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('organization'::"public"."entity_type", "org_id")
	);

-- Client Policies
CREATE POLICY "Organization editors and admins can insert clients" ON "public"."client" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'organization'::"public"."entity_type",
			"org_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Organization editors and admins can update clients" ON "public"."client"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'organization'::"public"."entity_type",
			"org_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'organization'::"public"."entity_type",
			"org_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Users can view clients they have access to" ON "public"."client" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('client'::"public"."entity_type", "client_id")
	);

-- Project Policies
CREATE POLICY "Client editors and admins can insert projects" ON "public"."project" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'client'::"public"."entity_type",
			"client_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project editors and owners can update projects" ON "public"."project"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Users can view projects they have access to" ON "public"."project" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ('project'::"public"."entity_type", "project_id")
	);

-- Membership Policies
CREATE POLICY "Admins can manage memberships" ON "public"."membership" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			"entity_type",
			"entity_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Admins can update memberships" ON "public"."membership"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			"entity_type",
			"entity_id",
			'admin'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			"entity_type",
			"entity_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Admins can delete memberships" ON "public"."membership" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		"entity_type",
		"entity_id",
		'admin'::"public"."membership_role"
	)
);

CREATE POLICY "Users can view memberships for entities they have access to" ON "public"."membership" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" ("entity_type", "entity_id")
	);

-- Invite Policies
CREATE POLICY "Admins can manage invites" ON "public"."invite" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			("resource_type"::text)::"public"."entity_type",
			"resource_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Admins can update invites" ON "public"."invite"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			("resource_type"::text)::"public"."entity_type",
			"resource_id",
			'admin'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			("resource_type"::text)::"public"."entity_type",
			"resource_id",
			'admin'::"public"."membership_role"
		)
	);

CREATE POLICY "Users can view invites for entities they have access to" ON "public"."invite" FOR
SELECT
	TO "authenticated" USING (
		"public"."current_user_has_entity_access" (
			("resource_type"::text)::"public"."entity_type",
			"resource_id"
		)
	);

-- Project Stage Policies
CREATE POLICY "Project Editors and Owners can insert project stage" ON "public"."project_stage" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project Editors and Owners can update project stage" ON "public"."project_stage"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
		)
	)
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project Editors and Owners can delete project stage" ON "public"."project_stage" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" ("project_stage"."project_id") AS "can_modify_project"
	)
);

CREATE POLICY "Project Viewers, Editors, and Owners can view project stage" ON "public"."project_stage" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" ("project_stage"."project_id") AS "can_access_project"
		)
	);

-- Budget Line Item Policies
CREATE POLICY "Project editors can insert budget line item" ON "public"."budget_line_item_current" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can update budget line item" ON "public"."budget_line_item_current"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
		)
	)
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can delete budget line item" ON "public"."budget_line_item_current" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" ("budget_line_item_current"."project_id") AS "can_modify_project"
	)
);

CREATE POLICY "Project viewers can view budget line item" ON "public"."budget_line_item_current" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" ("budget_line_item_current"."project_id") AS "can_access_project"
		)
	);

-- Budget Snapshot Policies
CREATE POLICY "Project viewers can insert budget snapshot" ON "public"."budget_snapshot" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project viewers can view budget snapshot" ON "public"."budget_snapshot" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "budget_snapshot"."project_stage_id"
							)
					)
				) AS "can_access_project"
		)
	);

CREATE POLICY "Project viewers can view budget snapshot line item" ON "public"."budget_snapshot_line_item" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"budget_snapshot"."project_stage_id"
									FROM
										"public"."budget_snapshot"
									WHERE
										(
											"budget_snapshot"."budget_snapshot_id" = "budget_snapshot_line_item"."budget_snapshot_id"
										)
								)
							)
					)
				) AS "can_access_project"
		)
	);

-- Risk Register Policies
CREATE POLICY "Project Editors and Owners can insert risks" ON "public"."risk_register" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Editors and Owners can update risks" ON "public"."risk_register"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Owners can delete risks" ON "public"."risk_register" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'owner'::"public"."membership_role"
	)
);

CREATE POLICY "Users can view risks for projects they have access to" ON "public"."risk_register" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

-- Approved Changes Policies
CREATE POLICY "Project Editors and Owners can insert approved changes" ON "public"."approved_changes" FOR INSERT TO "authenticated"
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Editors and Owners can update approved changes" ON "public"."approved_changes"
FOR UPDATE
	TO "authenticated" USING (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	)
WITH
	CHECK (
		"public"."current_user_has_entity_role" (
			'project'::"public"."entity_type",
			"project_id",
			'editor'::"public"."membership_role"
		)
	);

CREATE POLICY "Project Owners can delete approved changes" ON "public"."approved_changes" FOR DELETE TO "authenticated" USING (
	"public"."current_user_has_entity_role" (
		'project'::"public"."entity_type",
		"project_id",
		'owner'::"public"."membership_role"
	)
);

CREATE POLICY "Users can view approved changes for projects they have access t" ON "public"."approved_changes" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

-- Gateway Checklist Item Policies
CREATE POLICY "Project editors can insert gateway checklist item" ON "public"."gateway_checklist_item" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can update gateway checklist item" ON "public"."gateway_checklist_item"
FOR UPDATE
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	)
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project editors can delete gateway checklist item" ON "public"."gateway_checklist_item" FOR DELETE TO "authenticated" USING (
	(
		SELECT
			"public"."can_modify_project" (
				(
					SELECT
						"project_stage"."project_id"
					FROM
						"public"."project_stage"
					WHERE
						(
							"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
						)
				)
			) AS "can_modify_project"
	)
);

CREATE POLICY "Project viewers can view gateway checklist item" ON "public"."gateway_checklist_item" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item"."project_stage_id"
							)
					)
				) AS "can_access_project"
		)
	);

-- Gateway Checklist Item Status Log Policies
CREATE POLICY "Project editors can insert gateway checklist item status log" ON "public"."gateway_checklist_item_status_log" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			SELECT
				"public"."can_modify_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"gateway_checklist_item"."project_stage_id"
									FROM
										"public"."gateway_checklist_item"
									WHERE
										(
											"gateway_checklist_item"."gateway_checklist_item_id" = "gateway_checklist_item_status_log"."gateway_checklist_item_id"
										)
								)
							)
					)
				) AS "can_modify_project"
		)
	);

CREATE POLICY "Project viewers can view gateway checklist item status log" ON "public"."gateway_checklist_item_status_log" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = (
									SELECT
										"gateway_checklist_item"."project_stage_id"
									FROM
										"public"."gateway_checklist_item"
									WHERE
										(
											"gateway_checklist_item"."gateway_checklist_item_id" = "gateway_checklist_item_status_log"."gateway_checklist_item_id"
										)
								)
							)
					)
				) AS "can_access_project"
		)
	);

-- Project Gateway Stage Info Policies
CREATE POLICY "Users can insert gateway stage info for projects they can edit" ON "public"."project_gateway_stage_info" FOR INSERT TO "authenticated"
WITH
	CHECK (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_modify_project" ("ps"."project_id")
					)
			)
		)
	);

CREATE POLICY "Users can update gateway stage info for projects they can edit" ON "public"."project_gateway_stage_info"
FOR UPDATE
	USING (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_modify_project" ("ps"."project_id")
					)
			)
		)
	)
WITH
	CHECK (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_modify_project" ("ps"."project_id")
					)
			)
		)
	);

CREATE POLICY "Users can delete gateway stage info for projects they can edit" ON "public"."project_gateway_stage_info" FOR DELETE USING (
	(
		EXISTS (
			SELECT
				1
			FROM
				"public"."project_stage" "ps"
			WHERE
				(
					(
						"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
					)
					AND "public"."current_user_has_entity_role" (
						'project'::"public"."entity_type",
						"ps"."project_id",
						'owner'::"public"."membership_role"
					)
				)
		)
	)
);

CREATE POLICY "Users can view stage info for projects they can view" ON "public"."project_gateway_stage_info" FOR
SELECT
	USING (
		(
			EXISTS (
				SELECT
					1
				FROM
					"public"."project_stage" "ps"
				WHERE
					(
						(
							"ps"."project_stage_id" = "project_gateway_stage_info"."project_stage_id"
						)
						AND "public"."can_access_project" ("ps"."project_id")
					)
			)
		)
	);

-- WBS Library Policies
CREATE POLICY "Users can view WBS library" ON "public"."wbs_library" FOR
SELECT
	TO "authenticated" USING (true);

CREATE POLICY "Service role can insert WBS library" ON "public"."wbs_library" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Service role can update WBS library" ON "public"."wbs_library"
FOR UPDATE
	TO "service_role" USING (true);

CREATE POLICY "Service role can delete WBS library" ON "public"."wbs_library" FOR DELETE TO "service_role" USING (true);

-- WBS Library Item Policies
CREATE POLICY "Users can view standard WBS library item" ON "public"."wbs_library_item" FOR
SELECT
	TO "authenticated" USING (
		(
			"item_type" = 'Standard'::"public"."wbs_item_type"
		)
	);

CREATE POLICY "Service role can insert standard WBS library item" ON "public"."wbs_library_item" FOR INSERT TO "service_role"
WITH
	CHECK (
		(
			"item_type" = 'Standard'::"public"."wbs_item_type"
		)
	);

CREATE POLICY "Service role can update standard WBS library item" ON "public"."wbs_library_item"
FOR UPDATE
	TO "service_role" USING (
		(
			"item_type" = 'Standard'::"public"."wbs_item_type"
		)
	);

CREATE POLICY "Service role can delete standard WBS library item" ON "public"."wbs_library_item" FOR DELETE TO "service_role" USING (
	(
		"item_type" = 'Standard'::"public"."wbs_item_type"
	)
);

-- Audit Table Policies
CREATE POLICY "System can insert approved changes audit records" ON "public"."approved_changes_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view approved changes audit for accessible projects" ON "public"."approved_changes_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

CREATE POLICY "System can insert budget line item audit records" ON "public"."budget_line_item_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view budget line item audit for accessible projects" ON "public"."budget_line_item_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

CREATE POLICY "System can insert gateway checklist item audit records" ON "public"."gateway_checklist_item_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view gateway checklist item audit for accessible projects" ON "public"."gateway_checklist_item_audit" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "gateway_checklist_item_audit"."project_stage_id"
							)
					)
				) AS "can_access_project"
		)
	);

CREATE POLICY "System can insert project gateway stage info audit records" ON "public"."project_gateway_stage_info_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view project gateway stage info audit for accessible projects" ON "public"."project_gateway_stage_info_audit" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "project_gateway_stage_info_audit"."project_stage_id"
							)
					)
				) AS "can_access_project"
		)
	);

CREATE POLICY "System can insert project stage audit records" ON "public"."project_stage_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view project stage audit for accessible projects" ON "public"."project_stage_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

CREATE POLICY "System can insert risk register audit records" ON "public"."risk_register_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view risk register audit for accessible projects" ON "public"."risk_register_audit" FOR
SELECT
	TO "authenticated" USING ("public"."can_access_project" ("project_id"));

CREATE POLICY "System can insert WBS library item audit records" ON "public"."wbs_library_item_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view standard WBS library item audit records" ON "public"."wbs_library_item_audit" FOR
SELECT
	TO "authenticated" USING (("item_type" = 'Standard'::"text"));

CREATE POLICY "Users can view custom WBS library item audit for accessible projects" ON "public"."wbs_library_item_audit" FOR
SELECT
	TO "authenticated" USING (
		(
			("item_type" = 'Custom'::"text")
			AND (
				("project_id" IS NOT NULL)
				AND "public"."can_access_project" ("project_id")
			)
		)
	);
