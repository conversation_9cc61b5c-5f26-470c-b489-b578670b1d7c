-- Gateway Checklists
-- This file contains tables and functions for managing project stage gateway checklists
-- Gateway Checklist Item table
CREATE TABLE IF NOT EXISTS "public"."gateway_checklist_item" (
	"gateway_checklist_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_stage_id" "uuid" NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."gateway_checklist_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."gateway_checklist_item" IS 'Checklist items that must be completed before a stage gateway can be signed off';

-- Gateway Checklist Item Status Log table
CREATE TABLE IF NOT EXISTS "public"."gateway_checklist_item_status_log" (
	"log_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"gateway_checklist_item_id" "uuid" NOT NULL,
	"status" "public"."checklist_item_status" DEFAULT 'Incomplete'::"public"."checklist_item_status" NOT NULL,
	"updated_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"valid_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"latest" boolean DEFAULT false NOT NULL
);

ALTER TABLE "public"."gateway_checklist_item_status_log" OWNER TO "postgres";

COMMENT ON TABLE "public"."gateway_checklist_item_status_log" IS 'Status history log for gateway checklist items with temporal tracking';

-- Primary key constraints
ALTER TABLE ONLY "public"."gateway_checklist_item"
ADD CONSTRAINT "gateway_checklist_item_pkey" PRIMARY KEY ("gateway_checklist_item_id");

ALTER TABLE ONLY "public"."gateway_checklist_item_status_log"
ADD CONSTRAINT "gateway_checklist_item_status_log_pkey" PRIMARY KEY ("log_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."gateway_checklist_item"
ADD CONSTRAINT "gateway_checklist_item_project_stage_id_fkey" FOREIGN KEY ("project_stage_id") REFERENCES "public"."project_stage" ("project_stage_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."gateway_checklist_item_status_log"
ADD CONSTRAINT "gateway_checklist_item_status_lo_gateway_checklist_item_id_fkey" FOREIGN KEY ("gateway_checklist_item_id") REFERENCES "public"."gateway_checklist_item" ("gateway_checklist_item_id") ON UPDATE RESTRICT ON DELETE CASCADE;

ALTER TABLE ONLY "public"."gateway_checklist_item_status_log"
ADD CONSTRAINT "gateway_checklist_item_status_log_updated_by_user_id_fkey" FOREIGN KEY ("updated_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "gateway_checklist_item_status_gateway_checklist_item_id_val_idx" ON "public"."gateway_checklist_item_status_log" USING "btree" ("gateway_checklist_item_id", "valid_at" DESC);

CREATE UNIQUE INDEX "gateway_checklist_item_status_log_gateway_checklist_item_id_idx" ON "public"."gateway_checklist_item_status_log" USING "btree" ("gateway_checklist_item_id")
WHERE
	("latest" = true);

-- Enable Row Level Security
ALTER TABLE "public"."gateway_checklist_item" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."gateway_checklist_item_status_log" ENABLE ROW LEVEL SECURITY;

-- Function to set latest status flag
CREATE OR REPLACE FUNCTION "public"."set_gateway_checklist_item_latest" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN 
	-- Set all previous entries for this checklist item to not latest
	UPDATE public.gateway_checklist_item_status_log
	SET latest = FALSE
	WHERE gateway_checklist_item_id = NEW.gateway_checklist_item_id
		AND log_id != NEW.log_id;
	
	-- Set the new entry as latest
	UPDATE public.gateway_checklist_item_status_log
	SET latest = TRUE
	WHERE log_id = NEW.log_id;
	
	RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."set_gateway_checklist_item_latest" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."set_gateway_checklist_item_latest" () IS 'Ensures only one status log entry per checklist item is marked as latest';

-- Function to log initial checklist item status
CREATE OR REPLACE FUNCTION "public"."log_initial_checklist_item_status" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$ 
BEGIN 
	-- Insert a new status log entry for the newly created checklist item
	INSERT INTO public.gateway_checklist_item_status_log (
		gateway_checklist_item_id,
		status,
		updated_by_user_id,
		valid_at,
		latest
	)
	VALUES (
		NEW.gateway_checklist_item_id,
		'Incomplete', -- Default initial status
		auth.uid(), -- Current user who created the item
		now(), -- Current timestamp
		TRUE -- This is the latest status since it's the first one
	);
	
	RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."log_initial_checklist_item_status" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."log_initial_checklist_item_status" () IS 'Automatically creates initial status log entry when a checklist item is created';

-- Function to check if stage is ready for completion
CREATE OR REPLACE FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE 
	incomplete_count INTEGER;
BEGIN 
	-- Count incomplete checklist items for this stage
	SELECT COUNT(*) INTO incomplete_count
	FROM public.gateway_checklist_item gci
	JOIN public.gateway_checklist_item_status_log gcisl ON gci.gateway_checklist_item_id = gcisl.gateway_checklist_item_id
	WHERE gci.project_stage_id = p_project_stage_id
		AND gcisl.latest = TRUE
		AND gcisl.status = 'Incomplete';
	
	-- Stage is ready if there are no incomplete items
	RETURN incomplete_count = 0;
END;
$$;

ALTER FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."is_stage_ready_for_completion" ("p_project_stage_id" "uuid") IS 'Checks if all checklist items for a stage are complete or deferred';

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."gateway_checklist_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."gateway_checklist_item_status_log" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Trigger to automatically log initial status when a checklist item is created
CREATE OR REPLACE TRIGGER "trg_gateway_checklist_item_insert"
AFTER INSERT ON "public"."gateway_checklist_item" FOR EACH ROW
EXECUTE FUNCTION "public"."log_initial_checklist_item_status" ();

-- Trigger to manage latest status flag
CREATE OR REPLACE TRIGGER "trg_gateway_checklist_item_status_insert"
AFTER INSERT ON "public"."gateway_checklist_item_status_log" FOR EACH ROW
EXECUTE FUNCTION "public"."set_gateway_checklist_item_latest" ();
