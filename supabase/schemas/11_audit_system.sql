-- Audit System
-- This file contains all audit tables and their corresponding trigger functions for comprehensive change tracking
-- Approved Changes Audit table
CREATE TABLE IF NOT EXISTS "public"."approved_changes_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"approved_change_id" "uuid",
	"project_id" "uuid",
	"title" "text",
	"description" "text",
	"status" "text",
	"wbs_library_item_id" "uuid",
	"date_identified" "date",
	"date_approved" "date",
	"cause" "text",
	"effect" "text",
	"program_impact" "text",
	"potential_impact" numeric(15, 2),
	"mitigation_plan" "text",
	"date_for_review" "date",
	"risk_owner_user_id" "uuid",
	"risk_owner_name" "text",
	"risk_owner_email" "text",
	"approved_by_user_id" "uuid",
	"original_risk_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "approved_changes_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."approved_changes_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."approved_changes_audit" IS 'Audit log of all changes to approved changes entries';

-- Budget Line Item Audit table
CREATE TABLE IF NOT EXISTS "public"."budget_line_item_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"budget_line_item_id" "uuid",
	"project_id" "uuid",
	"wbs_library_item_id" "uuid",
	"quantity" numeric(15, 2),
	"unit" "text",
	"material_rate" numeric(15, 2),
	"labor_rate" numeric(15, 2),
	"productivity_per_hour" numeric(15, 2),
	"unit_rate_manual_override" boolean,
	"unit_rate" numeric(15, 2),
	"factor" numeric(15, 2),
	"remarks" "text",
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "budget_line_item_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."budget_line_item_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_line_item_audit" IS 'Audit log of all changes to budget line items';

-- Gateway Checklist Item Audit table
CREATE TABLE IF NOT EXISTS "public"."gateway_checklist_item_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"gateway_checklist_item_id" "uuid",
	"project_stage_id" "uuid",
	"name" "text",
	"description" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "gateway_checklist_item_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."gateway_checklist_item_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."gateway_checklist_item_audit" IS 'Audit log of all changes to gateway checklist items';

-- Project Gateway Stage Info Audit table
CREATE TABLE IF NOT EXISTS "public"."project_gateway_stage_info_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"project_gateway_stage_info_id" "uuid",
	"project_stage_id" "uuid",
	"basement_floors" numeric(15, 2),
	"ground_floor" numeric(15, 2),
	"upper_floors" numeric(15, 2),
	"total_gross_internal_floor_area" numeric(15, 2),
	"usable_area" numeric(15, 2),
	"circulation_area" numeric(15, 2),
	"ancillary_areas" numeric(15, 2),
	"internal_divisions" numeric(15, 2),
	"spaces_not_enclosed" numeric(15, 2),
	"total_gross_internal_floor_area_2" numeric(15, 2),
	"internal_cube" numeric(15, 2),
	"area_of_lowest_floor" numeric(15, 2),
	"site_area" numeric(15, 2),
	"number_of_units" numeric(15, 2),
	"nr_of_storeys" numeric(15, 2),
	"nr_of_storeys_primary" numeric(15, 2),
	"nr_of_storeys_secondary" numeric(15, 2),
	"basement_storeys_included_above" numeric(15, 2),
	"average_storey_height" numeric(15, 2),
	"below_ground_floors" numeric(15, 2),
	"ground_floor_height" numeric(15, 2),
	"above_ground_floors" numeric(15, 2),
	"external_vertical_envelope" numeric(15, 2),
	"additional_data" "jsonb",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "project_gateway_stage_info_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."project_gateway_stage_info_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_gateway_stage_info_audit" IS 'Audit log of all changes to project gateway stage info';

-- Project Stage Audit table
CREATE TABLE IF NOT EXISTS "public"."project_stage_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"project_stage_id" "uuid",
	"project_id" "uuid",
	"name" "text",
	"description" "text",
	"stage_order" integer,
	"stage" integer,
	"gateway_qualitative_scorecard" "jsonb",
	"date_started" timestamp with time zone,
	"date_completed" timestamp with time zone,
	"completion_notes" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "project_stage_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."project_stage_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_stage_audit" IS 'Audit log of all changes to project stages';

-- Risk Register Audit table
CREATE TABLE IF NOT EXISTS "public"."risk_register_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"risk_id" "uuid",
	"project_id" "uuid",
	"title" "text",
	"description" "text",
	"status" "text",
	"wbs_library_item_id" "uuid",
	"date_identified" "date",
	"cause" "text",
	"effect" "text",
	"program_impact" "text",
	"probability" numeric(5, 2),
	"potential_impact" numeric(15, 2),
	"mitigation_plan" "text",
	"date_for_review" "date",
	"risk_owner_user_id" "uuid",
	"risk_owner_name" "text",
	"risk_owner_email" "text",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "risk_register_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."risk_register_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."risk_register_audit" IS 'Audit log of all changes to risk register entries';

-- WBS Library Item Audit table
CREATE TABLE IF NOT EXISTS "public"."wbs_library_item_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"wbs_library_item_id" "uuid",
	"wbs_library_id" "uuid",
	"level" integer,
	"in_level_code" "text",
	"parent_item_id" "uuid",
	"code" "text",
	"description" "text",
	"cost_scope" "text",
	"item_type" "text",
	"client_id" "uuid",
	"project_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "wbs_library_item_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."wbs_library_item_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."wbs_library_item_audit" IS 'Audit log of all changes to WBS library items';

-- Primary key constraints for all audit tables
ALTER TABLE ONLY "public"."approved_changes_audit"
ADD CONSTRAINT "approved_changes_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."budget_line_item_audit"
ADD CONSTRAINT "budget_line_item_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."gateway_checklist_item_audit"
ADD CONSTRAINT "gateway_checklist_item_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."project_gateway_stage_info_audit"
ADD CONSTRAINT "project_gateway_stage_info_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."project_stage_audit"
ADD CONSTRAINT "project_stage_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."risk_register_audit"
ADD CONSTRAINT "risk_register_audit_pkey" PRIMARY KEY ("audit_id");

ALTER TABLE ONLY "public"."wbs_library_item_audit"
ADD CONSTRAINT "wbs_library_item_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints for audit tables
ALTER TABLE ONLY "public"."approved_changes_audit"
ADD CONSTRAINT "approved_changes_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_line_item_audit"
ADD CONSTRAINT "budget_line_item_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."gateway_checklist_item_audit"
ADD CONSTRAINT "gateway_checklist_item_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project_gateway_stage_info_audit"
ADD CONSTRAINT "project_gateway_stage_info_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."project_stage_audit"
ADD CONSTRAINT "project_stage_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."risk_register_audit"
ADD CONSTRAINT "risk_register_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."wbs_library_item_audit"
ADD CONSTRAINT "wbs_library_item_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for audit tables performance
CREATE INDEX "idx_approved_changes_audit_changed_at" ON "public"."approved_changes_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_approved_changes_audit_changed_by" ON "public"."approved_changes_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_approved_changes_audit_project_id" ON "public"."approved_changes_audit" USING "btree" ("project_id");

CREATE INDEX "idx_budget_line_item_audit_changed_at" ON "public"."budget_line_item_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_budget_line_item_audit_changed_by" ON "public"."budget_line_item_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_budget_line_item_audit_project_id" ON "public"."budget_line_item_audit" USING "btree" ("project_id");

CREATE INDEX "idx_gateway_checklist_item_audit_changed_at" ON "public"."gateway_checklist_item_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_gateway_checklist_item_audit_changed_by" ON "public"."gateway_checklist_item_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_gateway_checklist_item_audit_project_stage_id" ON "public"."gateway_checklist_item_audit" USING "btree" ("project_stage_id");

CREATE INDEX "idx_project_gateway_stage_info_audit_changed_at" ON "public"."project_gateway_stage_info_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_project_gateway_stage_info_audit_changed_by" ON "public"."project_gateway_stage_info_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_project_gateway_stage_info_audit_project_stage_id" ON "public"."project_gateway_stage_info_audit" USING "btree" ("project_stage_id");

CREATE INDEX "idx_project_stage_audit_changed_at" ON "public"."project_stage_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_project_stage_audit_changed_by" ON "public"."project_stage_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_project_stage_audit_project_id" ON "public"."project_stage_audit" USING "btree" ("project_id");

CREATE INDEX "idx_risk_register_audit_changed_at" ON "public"."risk_register_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_risk_register_audit_changed_by" ON "public"."risk_register_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_risk_register_audit_project_id" ON "public"."risk_register_audit" USING "btree" ("project_id");

CREATE INDEX "idx_wbs_library_item_audit_changed_at" ON "public"."wbs_library_item_audit" USING "btree" ("changed_at");

CREATE INDEX "idx_wbs_library_item_audit_changed_by" ON "public"."wbs_library_item_audit" USING "btree" ("changed_by");

CREATE INDEX "idx_wbs_library_item_audit_wbs_library_item_id" ON "public"."wbs_library_item_audit" USING "btree" ("wbs_library_item_id");

-- Enable Row Level Security for all audit tables
ALTER TABLE "public"."approved_changes_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."budget_line_item_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."gateway_checklist_item_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."project_gateway_stage_info_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."project_stage_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."risk_register_audit" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."wbs_library_item_audit" ENABLE ROW LEVEL SECURITY;

-- Audit Trigger Functions
-- Approved Changes Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_approved_changes_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, old_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.approved_change_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.date_approved, OLD.cause, OLD.effect, OLD.program_impact, OLD.potential_impact,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.approved_by_user_id, OLD.original_risk_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.approved_change_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.date_approved, NEW.cause, NEW.effect, NEW.program_impact, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.approved_by_user_id, NEW.original_risk_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.approved_changes_audit (
            operation_type, changed_by, changed_at, new_values,
            approved_change_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, date_approved, cause, effect, program_impact, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            approved_by_user_id, original_risk_id, created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.approved_change_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.date_approved, NEW.cause, NEW.effect, NEW.program_impact, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.approved_by_user_id, NEW.original_risk_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_approved_changes_changes" () OWNER TO "postgres";

-- Budget Line Item Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_budget_line_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like the generate_demo_budget_data() function
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_line_item_id, OLD.project_id, OLD.wbs_library_item_id, OLD.quantity, OLD.unit,
            OLD.material_rate, OLD.labor_rate, OLD.productivity_per_hour, OLD.unit_rate_manual_override,
            OLD.unit_rate, OLD.factor, OLD.remarks, OLD.cost_certainty, OLD.design_certainty, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_budget_line_item_changes" () OWNER TO "postgres";

-- Gateway Checklist Item Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_gateway_checklist_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, old_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.gateway_checklist_item_id, OLD.project_stage_id, OLD.name, OLD.description,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.gateway_checklist_item_id, NEW.project_stage_id, NEW.name, NEW.description,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.gateway_checklist_item_audit (
            operation_type, changed_by, changed_at, new_values,
            gateway_checklist_item_id, project_stage_id, name, description,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.gateway_checklist_item_id, NEW.project_stage_id, NEW.name, NEW.description,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_gateway_checklist_item_changes" () OWNER TO "postgres";

-- Risk Register Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_risk_register_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.risk_id, OLD.project_id, OLD.title, OLD.description, OLD.status, OLD.wbs_library_item_id,
            OLD.date_identified, OLD.cause, OLD.effect, OLD.program_impact, OLD.probability, OLD.potential_impact,
            OLD.mitigation_plan, OLD.date_for_review, OLD.risk_owner_user_id, OLD.risk_owner_name, OLD.risk_owner_email,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.probability, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.risk_register_audit (
            operation_type, changed_by, changed_at, new_values,
            risk_id, project_id, title, description, status, wbs_library_item_id,
            date_identified, cause, effect, program_impact, probability, potential_impact,
            mitigation_plan, date_for_review, risk_owner_user_id, risk_owner_name, risk_owner_email,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.risk_id, NEW.project_id, NEW.title, NEW.description, NEW.status, NEW.wbs_library_item_id,
            NEW.date_identified, NEW.cause, NEW.effect, NEW.program_impact, NEW.probability, NEW.potential_impact,
            NEW.mitigation_plan, NEW.date_for_review, NEW.risk_owner_user_id, NEW.risk_owner_name, NEW.risk_owner_email,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_risk_register_changes" () OWNER TO "postgres";

-- Project Stage Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_project_stage_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, old_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.project_stage_id, OLD.project_id, OLD.name, OLD.description, OLD.stage_order, OLD.stage,
            OLD.gateway_qualitative_scorecard, OLD.date_started, OLD.date_completed, OLD.completion_notes,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_stage_id, NEW.project_id, NEW.name, NEW.description, NEW.stage_order, NEW.stage,
            NEW.gateway_qualitative_scorecard, NEW.date_started, NEW.date_completed, NEW.completion_notes,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_stage_audit (
            operation_type, changed_by, changed_at, new_values,
            project_stage_id, project_id, name, description, stage_order, stage,
            gateway_qualitative_scorecard, date_started, date_completed, completion_notes,
            created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.project_stage_id, NEW.project_id, NEW.name, NEW.description, NEW.stage_order, NEW.stage,
            NEW.gateway_qualitative_scorecard, NEW.date_started, NEW.date_completed, NEW.completion_notes,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSE
        RAISE EXCEPTION 'Unexpected TG_OP value: %', TG_OP;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_project_stage_changes" () OWNER TO "postgres";

-- WBS Library Item Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_wbs_library_item_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like seed data or system operations
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, old_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.wbs_library_item_id, OLD.wbs_library_id, OLD.level, OLD.in_level_code, OLD.parent_item_id,
            OLD.code, OLD.description, OLD.cost_scope, OLD.item_type, OLD.client_id, OLD.project_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.wbs_library_item_id, NEW.wbs_library_id, NEW.level, NEW.in_level_code, NEW.parent_item_id,
            NEW.code, NEW.description, NEW.cost_scope, NEW.item_type, NEW.client_id, NEW.project_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, new_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.wbs_library_item_id, NEW.wbs_library_id, NEW.level, NEW.in_level_code, NEW.parent_item_id,
            NEW.code, NEW.description, NEW.cost_scope, NEW.item_type, NEW.client_id, NEW.project_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_wbs_library_item_changes" () OWNER TO "postgres";

-- Project Gateway Stage Info Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_project_gateway_stage_info_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, old_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.project_gateway_stage_info_id, OLD.project_stage_id, OLD.basement_floors, OLD.ground_floor, OLD.upper_floors,
            OLD.total_gross_internal_floor_area, OLD.usable_area, OLD.circulation_area, OLD.ancillary_areas, OLD.internal_divisions,
            OLD.spaces_not_enclosed, OLD.total_gross_internal_floor_area_2, OLD.internal_cube, OLD.area_of_lowest_floor,
            OLD.site_area, OLD.number_of_units, OLD.nr_of_storeys, OLD.nr_of_storeys_primary, OLD.nr_of_storeys_secondary,
            OLD.basement_storeys_included_above, OLD.average_storey_height, OLD.below_ground_floors, OLD.ground_floor_height,
            OLD.above_ground_floors, OLD.external_vertical_envelope, OLD.additional_data, OLD.created_by_user_id,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_gateway_stage_info_id, NEW.project_stage_id, NEW.basement_floors, NEW.ground_floor, NEW.upper_floors,
            NEW.total_gross_internal_floor_area, NEW.usable_area, NEW.circulation_area, NEW.ancillary_areas, NEW.internal_divisions,
            NEW.spaces_not_enclosed, NEW.total_gross_internal_floor_area_2, NEW.internal_cube, NEW.area_of_lowest_floor,
            NEW.site_area, NEW.number_of_units, NEW.nr_of_storeys, NEW.nr_of_storeys_primary, NEW.nr_of_storeys_secondary,
            NEW.basement_storeys_included_above, NEW.average_storey_height, NEW.below_ground_floors, NEW.ground_floor_height,
            NEW.above_ground_floors, NEW.external_vertical_envelope, NEW.additional_data, NEW.created_by_user_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, new_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.project_gateway_stage_info_id, NEW.project_stage_id, NEW.basement_floors, NEW.ground_floor, NEW.upper_floors,
            NEW.total_gross_internal_floor_area, NEW.usable_area, NEW.circulation_area, NEW.ancillary_areas, NEW.internal_divisions,
            NEW.spaces_not_enclosed, NEW.total_gross_internal_floor_area_2, NEW.internal_cube, NEW.area_of_lowest_floor,
            NEW.site_area, NEW.number_of_units, NEW.nr_of_storeys, NEW.nr_of_storeys_primary, NEW.nr_of_storeys_secondary,
            NEW.basement_storeys_included_above, NEW.average_storey_height, NEW.below_ground_floors, NEW.ground_floor_height,
            NEW.above_ground_floors, NEW.external_vertical_envelope, NEW.additional_data, NEW.created_by_user_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_project_gateway_stage_info_changes" () OWNER TO "postgres";

-- Audit Triggers
CREATE OR REPLACE TRIGGER "audit_approved_changes_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."approved_changes" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_approved_changes_changes" ();

CREATE OR REPLACE TRIGGER "audit_budget_line_item_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."budget_line_item_current" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_budget_line_item_changes" ();

CREATE OR REPLACE TRIGGER "audit_gateway_checklist_item_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."gateway_checklist_item" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_gateway_checklist_item_changes" ();

CREATE OR REPLACE TRIGGER "audit_project_gateway_stage_info_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."project_gateway_stage_info" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_project_gateway_stage_info_changes" ();

CREATE OR REPLACE TRIGGER "audit_project_stage_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."project_stage" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_project_stage_changes" ();

CREATE OR REPLACE TRIGGER "audit_risk_register_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."risk_register" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_risk_register_changes" ();

CREATE OR REPLACE TRIGGER "audit_wbs_library_item_trigger"
AFTER INSERT
OR DELETE
OR
UPDATE ON "public"."wbs_library_item" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_wbs_library_item_changes" ();
