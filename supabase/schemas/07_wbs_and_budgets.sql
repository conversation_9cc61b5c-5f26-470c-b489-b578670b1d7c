-- WBS (Work Breakdown Structure) and Budget Management
-- This file contains tables and functions for managing WBS libraries and budget line items
-- WBS Library table
CREATE TABLE IF NOT EXISTS "public"."wbs_library" (
	"wbs_library_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"name" "text" NOT NULL,
	"description" "text",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."wbs_library" OWNER TO "postgres";

COMMENT ON TABLE "public"."wbs_library" IS 'Work breakdown structure library containing standardized cost lines for projects.';

-- WBS Library Item table
CREATE TABLE IF NOT EXISTS "public"."wbs_library_item" (
	"wbs_library_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"wbs_library_id" "uuid" NOT NULL,
	"level" integer NOT NULL,
	"in_level_code" "text" NOT NULL,
	"parent_item_id" "uuid",
	"code" "text" NOT NULL,
	"description" "text" NOT NULL,
	"cost_scope" "text",
	"item_type" "public"."wbs_item_type" DEFAULT 'Custom'::"public"."wbs_item_type" NOT NULL,
	"client_id" "uuid",
	"project_id" "uuid",
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."wbs_library_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."wbs_library_item" IS 'Individual items within a WBS library, organized hierarchically';

COMMENT ON COLUMN "public"."wbs_library_item"."level" IS 'Hierarchical level (1=top level, 2=sub-category, etc.)';

COMMENT ON COLUMN "public"."wbs_library_item"."in_level_code" IS 'Code within the current level (e.g., "01", "02")';

COMMENT ON COLUMN "public"."wbs_library_item"."code" IS 'Full hierarchical code (e.g., "01.02.03")';

COMMENT ON COLUMN "public"."wbs_library_item"."item_type" IS 'Standard items are from libraries like ICMS, Custom items are project-specific';

-- Budget Line Item Current table
CREATE TABLE IF NOT EXISTS "public"."budget_line_item_current" (
	"budget_line_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_id" "uuid" NOT NULL,
	"wbs_library_item_id" "uuid" NOT NULL,
	"quantity" numeric(20, 4) NOT NULL,
	"unit" "text",
	"material_rate" numeric(20, 4) NOT NULL,
	"labor_rate" numeric(20, 4),
	"productivity_per_hour" numeric(20, 4),
	"unit_rate_manual_override" boolean DEFAULT false NOT NULL,
	"unit_rate" numeric(20, 4) NOT NULL,
	"factor" numeric(20, 4),
	"remarks" "text",
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."budget_line_item_current" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_line_item_current" IS 'Current state of project budget line items';

-- Budget Snapshot table
CREATE TABLE IF NOT EXISTS "public"."budget_snapshot" (
	"budget_snapshot_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"project_stage_id" "uuid" NOT NULL,
	"freeze_date" timestamp with time zone DEFAULT "now" () NOT NULL,
	"freeze_reason" "text",
	"created_by_user_id" "uuid" NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."budget_snapshot" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_snapshot" IS 'Snapshots of budget state at specific project stages';

-- Budget Snapshot Line Item table
CREATE TABLE IF NOT EXISTS "public"."budget_snapshot_line_item" (
	"budget_snapshot_line_item_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"budget_snapshot_id" "uuid" NOT NULL,
	"wbs_library_item_id" "uuid" NOT NULL,
	"quantity" numeric(20, 4),
	"unit" "text",
	"material_rate" numeric(20, 4),
	"labor_rate" numeric(20, 4),
	"productivity_per_hour" numeric(20, 4),
	"unit_rate_manual_override" boolean DEFAULT false NOT NULL,
	"unit_rate" numeric(20, 4),
	"factor" numeric(20, 4),
	"remarks" "text",
	"cost_certainty" numeric(5, 2),
	"design_certainty" numeric(5, 2),
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."budget_snapshot_line_item" OWNER TO "postgres";

COMMENT ON TABLE "public"."budget_snapshot_line_item" IS 'Line items within budget snapshots';

-- Primary key constraints
ALTER TABLE ONLY "public"."wbs_library"
ADD CONSTRAINT "wbs_library_pkey" PRIMARY KEY ("wbs_library_id");

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_pkey" PRIMARY KEY ("wbs_library_item_id");

ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_pkey" PRIMARY KEY ("budget_line_item_id");

ALTER TABLE ONLY "public"."budget_snapshot"
ADD CONSTRAINT "budget_snapshot_pkey" PRIMARY KEY ("budget_snapshot_id");

ALTER TABLE ONLY "public"."budget_snapshot_line_item"
ADD CONSTRAINT "budget_snapshot_line_item_pkey" PRIMARY KEY ("budget_snapshot_line_item_id");

-- Unique constraints
ALTER TABLE ONLY "public"."wbs_library"
ADD CONSTRAINT "wbs_library_name_key" UNIQUE ("name");

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_wbs_library_id_code_key" UNIQUE ("wbs_library_id", "code");

ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_project_id_wbs_library_item_id_key" UNIQUE ("project_id", "wbs_library_item_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_wbs_library_id_fkey" FOREIGN KEY ("wbs_library_id") REFERENCES "public"."wbs_library" ("wbs_library_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_parent_item_id_fkey" FOREIGN KEY ("parent_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "public"."client" ("client_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."wbs_library_item"
ADD CONSTRAINT "wbs_library_item_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_line_item_current"
ADD CONSTRAINT "budget_line_item_current_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_snapshot"
ADD CONSTRAINT "budget_snapshot_project_stage_id_fkey" FOREIGN KEY ("project_stage_id") REFERENCES "public"."project_stage" ("project_stage_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_snapshot"
ADD CONSTRAINT "budget_snapshot_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_snapshot_line_item"
ADD CONSTRAINT "budget_snapshot_line_item_budget_snapshot_id_fkey" FOREIGN KEY ("budget_snapshot_id") REFERENCES "public"."budget_snapshot" ("budget_snapshot_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."budget_snapshot_line_item"
ADD CONSTRAINT "budget_snapshot_line_item_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "budget_line_item_current_project_id_idx" ON "public"."budget_line_item_current" USING "btree" ("project_id");

CREATE INDEX "budget_line_item_current_wbs_library_item_id_idx" ON "public"."budget_line_item_current" USING "btree" ("wbs_library_item_id");

CREATE INDEX "budget_snapshot_line_item_budget_snapshot_id_idx" ON "public"."budget_snapshot_line_item" USING "btree" ("budget_snapshot_id");

CREATE INDEX "wbs_library_item_parent_item_id_idx" ON "public"."wbs_library_item" USING "btree" ("parent_item_id");

CREATE INDEX "wbs_library_item_wbs_library_id_idx" ON "public"."wbs_library_item" USING "btree" ("wbs_library_id");

-- Enable Row Level Security
ALTER TABLE "public"."wbs_library" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."wbs_library_item" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."budget_line_item_current" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."budget_snapshot" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."budget_snapshot_line_item" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."wbs_library" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."wbs_library_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_line_item_current" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_snapshot" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."budget_snapshot_line_item" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();
